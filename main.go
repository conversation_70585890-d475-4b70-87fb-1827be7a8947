package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/infrastructure/mongodb"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/infrastructure/subscriber"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/service"
	"github.com/inv-cloud-platform/hub-com-auth-go/hubauth"
	"github.com/inv-cloud-platform/hub-com-metrics-go/hubmetrics"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"
	"github.com/rs/zerolog/log"
	"resty.dev/v3"
)

func main() {
	ctx := context.Background()
	log.Info().Msg("Application starting ...")
	cfg, err := config.NewConfig()
	if err != nil {
		log.Err(err).Msg("failed to load configuration")
		return
	}
	common.NewLogger(cfg.Logging.Level)
	token := hubauth.NewToken(
		cfg.Auth.KeycloakHost,
		cfg.Auth.KeycloakRealm,
		cfg.Auth.ClientSecret,
		cfg.Auth.ClientID,
		cfg.Auth.ServiceUsername,
		cfg.Auth.ServicePassword,
	)
	// Start auto refresh token goroutine
	go func() {
		log.Info().Msg("starting token auto refresh...")
		if authErr := token.StartAutoRefresh(ctx); authErr != nil {
			log.Err(authErr).Msg("failed to auto-refresh auth token")
			return
		}
	}()
	go func() {
		// monitoring config
		reporter, errNewMetrics := hubmetrics.NewReporter("fe-ev-csms-io")
		if errNewMetrics != nil {
			log.Err(errNewMetrics).Msg("unable to create new metrics reporter")
		}
		errMetrics := common.StartMetricsServer(cfg.Service, cfg.Service.ReadHeaderTimeout, reporter)
		if errMetrics != nil {
			log.Error().Err(errMetrics).Msg("unable to start metrics server")
		}
	}()
	mgr, err := common.SetupNats(ctx, cfg.NATS)
	if err != nil {
		log.Err(err).Msg("failed to connect NATs")
		return
	}
	mongoClient, err := hubmongo.ConnectV2(&hubmongo.Config{
		Schema:     cfg.Mongo.Schema,
		Host:       cfg.Mongo.Host,
		Port:       cfg.Mongo.Port,
		Username:   cfg.Mongo.Username,
		Password:   cfg.Mongo.Password,
		AuthSource: cfg.Mongo.AuthSource,
		Options:    cfg.Mongo.Options,
		Timeout:    cfg.Mongo.Timeout,
	})
	if err != nil {
		log.Info().Err(err).Msg("failed to open connection with mongo")
		return
	}
	log.Info().Msg("mongo database connected")
	db := mongoClient.Database(cfg.Mongo.Database)
	repo := mongodb.NewRepository(cfg, db)
	conSrv := service.NewConnectionService(cfg, repo)
	svc := service.NewAppService(cfg, resty.New(), conSrv, token)
	sub, err := subscriber.NewSubscriber(ctx, cfg, mgr, svc)
	if err != nil {
		log.Err(err).Msg("error to intantiate subscriber")
		return
	}
	if errStart := sub.Start(); errStart != nil {
		log.Err(errStart).Msg("consumer start error")
	}
	defer sub.Stop()
	terminate := make(chan os.Signal, 1)
	signal.Notify(terminate, os.Interrupt, syscall.SIGTERM)
	<-terminate
	log.Info().Msg("Application stopping ...")
	log.Info().Msg("service stopped.")
}
