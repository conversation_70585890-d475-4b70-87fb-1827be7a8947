version: '3.8'
services:
  mongo-csms-io:
    container_name: mongo-csms-io
    image: mongo:latest
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin_user
      MONGO_INITDB_ROOT_PASSWORD: Pass123!
      MONGO_INITDB_DATABASE: fe-ev-csms-io 
    volumes:
      - ./tests/e2e/db/mongo-csms-io:/docker-entrypoint-initdb.d:ro
      - ./tests/e2e/db/mongo-csms-io/data:/tmp/data:ro
    networks:
      - nats
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27018/test --quiet
      interval: 10s
      timeout: 10s
      retries: 3
networks:
  nats: