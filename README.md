Service Name: fe-ev-csms-io

Overview

This microservice is responsible for processing OCPI (Open Charge Point Interface) command messages received from a NATS messaging system. It validates and translates these commands and forwards them to the Driivz API via HTTP requests. Serving as a bridge between telemetry events from edge devices (EV chargers) and the Driivz platform, it ensures that commands like START_SESSION, STOP_SESSION, RESERVE_NOW, etc., are accurately handled and transmitted.

Features

Telemetry Event Processing: Subscribes to a NATS stream to receive telemetry events containing OCPI commands from edge devices.
OCPI Command Translation: Validates and translates domain-specific commands into OCPI-compliant commands.
HTTP Communication with Driivz: Sends translated commands to the Driivz API via HTTP requests.
Transaction Management: Manages the lifecycle of transactions, ensuring data consistency and proper error handling.

Key Components

Processor
Subscribes to NATS subjects to receive telemetry events.
Validates and parses incoming messages.
Manages transactions using the TransactionManager.
Translates commands using the OCPITranslator.
Communicates with Driivz via the HTTP client.
TransactionManager
Starts, completes, and fails transactions.
Ensures data consistency and handles transaction statuses.
Stores transaction data using the configured storage type.
OCPITranslator
Converts domain-specific commands into OCPI-compliant commands.
Translates responses from Driivz back into domain-specific results.
HTTP Client
Sends translated OCPI commands to Driivz.
Handles HTTP communication, including retries and error handling.
Configurable timeout and retry settings.
Error Handling and Retries

Implements retry logic for transient HTTP errors.
Logs errors with detailed context for troubleshooting.
Fails transactions gracefully when unrecoverable errors occur.
Logging and Monitoring

Uses structured logging with zap for clarity and consistency.
Logs key events, errors, and transaction details.
Can be integrated with monitoring tools for real-time insights.