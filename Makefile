APP_NAME=fe-ev-csms-io
TAG=0.0.1
GO_LINT=golangci/golangci-lint

.PHONY: update
## update: update dependencies
update:
	go get -u ./...

.PHONY: lint
## lint: run linters and static analysis tools to detect potential issues, bugs, and code style violations in Go codebases
lint:
	go mod vendor
	docker run -t --rm -v $(shell pwd):/app -w /app ${GO_LINT} golangci-lint run -v --fix

.PHONY: d-build
## d-build: build the docker image
d-build:
	docker build -f Dockerfile --build-arg APP_NAME=${APP_NAME} -t ${APP_NAME}:${TAG} .

.PHONY: d-run
## d-run: run the docker container
d-run:
	docker run --rm -it ${APP_NAME}:${TAG}

.PHONY: run
## run: local with default settings
run:
	docker compose up -d && sleep 10 && go run main.go

.PHONY: test
## test: run unit tests
test:
	go test -v ./...

.PHONY: down
## down: stop and remove docker containers
down:
	docker compose down

.PHONY: vendor
## vendor: download and verify dependencies
vendor:
	go mod vendor
	go mod verify

.PHONY: clean
## clean: clean build artifacts
clean:
	rm -rf vendor/
	rm -rf bin/
	rm -f ${APP_NAME}

.PHONY: build
## build: build the application
build:
	CGO_ENABLED=0 go build -o ${APP_NAME} main.go

.PHONY: all
## all: clean, lint, test, and build
all: clean vendor lint test build

# help: show this help message
help: Makefile
	@echo
	@echo " Choose a command to run in "$(APP_NAME)":"
	@echo
	@sed -n 's/^##//p' $< | column -t -s ':' |  sed -e 's/^/ /'
	@echo
