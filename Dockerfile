FROM golang:1.24-alpine AS build

COPY build-context /root/

WORKDIR /app

RUN apk update --no-cache && \
    apk add --no-cache \
    git \
    upx \
    ca-certificates \
    openssh-client

RUN mkdir -p -m 0600 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts

ENV GOPRIVATE=github.com/inv-cloud-platform
RUN git config --global url."**************:".insteadOf "https://github.com/"

COPY go.mod go.sum ./

RUN --mount=type=ssh go mod download

COPY . ./

RUN CGO_ENABLED=0 GOOS=linux go build \
    -ldflags "-s -w" \
    -o /app/fe-ev-csms-io

RUN upx -9 /app/fe-ev-csms-io

FROM alpine

COPY --from=build /app/fe-ev-csms-io /fe-ev-csms-io

ENTRYPOINT ["/fe-ev-csms-io"]
