version: "2"
run:
  go: "1.23"
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - cyclop
    - decorder
    - dogsled
    - errcheck
    - errname
    - errorlint
    - gocheckcompilerdirectives
    - gochecknoinits
    - gocritic
    - gocyclo
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - misspell
    - mnd
    - nakedret
    - noctx
    - nolintlint
    - perfsprint
    - revive
    - staticcheck
    - unconvert
    - unparam
    - unused
    - whitespace
  settings:
    cyclop:
      max-complexity: 15
    gocritic:
      disabled-checks:
        - ifElseChain
        - typeAssertChain
        - yodaStyleExpr
      enabled-tags:
        - diagnostic
        - performance
        - style
      settings:
        hugeParam:
          sizeThreshold: 256
        rangeValCopy:
          sizeThreshold: 256
    gocyclo:
      min-complexity: 50
    govet:
      disable:
        - fieldalignment
      enable-all: true
      settings:
        shadow:
          strict: true
    misspell:
      locale: US
    mnd:
      ignored-numbers:
        - "1"
        - "2"
        - "3"
    nolintlint:
      require-explanation: false
      require-specific: false
      allow-unused: false
    revive:
      rules:
        - name: unexported-return
          disabled: true
        - name: unused-parameter
        - name: unused-receiver
          severity: warning
        - name: unreachable-code
          severity: warning
    staticcheck:
      checks:
        - all
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - gocritic
          - gocyclo
          - gosec
          - staticcheck
          - unused
        path: _test\.go
      - linters:
          - unused
        path: generated.*\.go
      - linters:
          - revive
        path: _test\.go
        text: unused parameter
    paths:
      - .*\.generated\.go$
      - .*\.pb\.go$
      - vendor/
      - third_party/
      - third_party$
      - builtin$
      - examples$
issues:
  max-same-issues: 20
formatters:
  enable:
    - gci
    - gofmt
  settings:
    gofmt:
      rewrite-rules:
        - pattern: interface{}
          replacement: any
        - pattern: a[b:len(a)]
          replacement: a[b:]
    goimports:
      local-prefixes:
        - inv-cloud-platform
  exclusions:
    generated: lax
    paths:
      - .*\.generated\.go$
      - .*\.pb\.go$
      - vendor/
      - third_party/
      - third_party$
      - builtin$
      - examples$
