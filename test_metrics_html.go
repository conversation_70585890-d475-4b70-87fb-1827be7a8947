package main

import (
	"fmt"
	"html"
	"net/http"
	"strings"
)

func main() {
	// Sample metrics data (similar to what Prometheus would output)
	sampleMetrics := `# HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles.
# TYPE go_gc_duration_seconds summary
go_gc_duration_seconds{quantile="0"} 0
go_gc_duration_seconds{quantile="0.25"} 0
go_gc_duration_seconds{quantile="0.5"} 0
go_gc_duration_seconds{quantile="0.75"} 0
go_gc_duration_seconds{quantile="1"} 0
go_gc_duration_seconds_sum 0
go_gc_duration_seconds_count 0
# HELP appservice_resty_request_duration_seconds Duration of resty requests in seconds
# TYPE appservice_resty_request_duration_seconds histogram
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.005"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.01"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.025"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.05"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.1"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.25"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="0.5"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="1"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="2.5"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="5"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="10"} 0
appservice_resty_request_duration_seconds_bucket{endpoint="tariffs",status="200",le="+Inf"} 0
appservice_resty_request_duration_seconds_sum{endpoint="tariffs",status="200"} 0
appservice_resty_request_duration_seconds_count{endpoint="tariffs",status="200"} 0
# HELP fe_ev_csms_io_errors_total Total number of errors
# TYPE fe_ev_csms_io_errors_total counter
fe_ev_csms_io_errors_total 0`

	http.HandleFunc("/metrics-test", func(w http.ResponseWriter, r *http.Request) {
		// Check if request is from a browser
		acceptHeader := r.Header.Get("Accept")
		isBrowser := strings.Contains(acceptHeader, "text/html")

		if isBrowser {
			// Serve as HTML for browsers to prevent downloads
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			w.Header().Set("Cache-Control", "no-cache")
			w.WriteHeader(http.StatusOK)
			
			// Escape HTML characters in metrics content
			escapedMetrics := html.EscapeString(sampleMetrics)
			
			htmlContent := `<!DOCTYPE html>
<html>
<head>
    <title>Prometheus Metrics</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: 'Courier New', monospace; 
            margin: 20px; 
            background-color: #f8f9fa;
        }
        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        pre { 
            background: #fff; 
            padding: 20px; 
            border-radius: 8px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.4;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-line {
            margin: 2px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Prometheus Metrics</h1>
        <p>Raw endpoint: <a href="/metrics" target="_blank">/metrics</a> | Refresh: <a href="javascript:location.reload()">↻</a></p>
    </div>
    <pre class="metric-line">` + escapedMetrics + `</pre>
</body>
</html>`
			w.Write([]byte(htmlContent))
		} else {
			// Serve as plain text
			w.Header().Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(sampleMetrics))
		}
	})

	fmt.Println("Test server starting on http://localhost:8081/metrics-test")
	fmt.Println("Open in browser to see HTML formatted metrics")
	http.ListenAndServe(":8081", nil)
}
