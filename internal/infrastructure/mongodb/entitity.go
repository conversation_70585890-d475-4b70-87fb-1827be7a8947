package mongodb

import "github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"

type CredentialKey string

const (
	CPOCredentialKey  CredentialKey = "cpo"
	EMSPCredentialKey CredentialKey = "emsp"
)

type Connection struct {
	CountryCode string        `bson:"country_code"`
	PartyID     string        `bson:"party_code"`
	Endpoint    MapEndpoint   `bson:"endpoint"`
	Credential  MapCredential `bson:"credential"`
}

func (c *Connection) ToDomainConnection() *domain.Connection {
	domainEndpoints := make(domain.MapEndpoint)
	for key, endpoint := range c.Endpoint {
		domainEndpoints[key] = domain.Endpoint{
			Version:   endpoint.Version,
			Endpoints: make([]domain.EndpointItem, len(endpoint.Endpoints)),
		}
		for i, item := range endpoint.Endpoints {
			domainEndpoints[key].Endpoints[i] = domain.EndpointItem{
				Identifier: item.Identifier,
				URL:        item.URL,
				Role:       item.Role,
			}
		}
	}
	domainCredentials := make(domain.MapCredential)
	for key, cred := range c.Credential {
		domainRoles := make([]domain.Role, len(cred.Roles))
		for i, role := range cred.Roles {
			domainRoles[i] = domain.Role{
				Role:        role.Role,
				PartyID:     role.PartyID,
				CountryCode: role.CountryCode,
			}
		}
		domainKey := domain.CredentialKey(key)
		domainCredentials[domainKey] = domain.Credential{
			Token: cred.Token,
			URL:   cred.URL,
			Roles: domainRoles,
		}
	}
	return &domain.Connection{
		CountryCode: c.CountryCode,
		PartyID:     c.PartyID,
		Endpoint:    domainEndpoints,
		Credential:  domainCredentials,
	}
}

func (*Connection) ToEntityConnection(conn *domain.Connection) *Connection {
	entityEndpoints := make(map[string]Endpoint)
	for key, domainEndpoint := range conn.Endpoint {
		entityEndpointItems := make([]EndpointItem, len(domainEndpoint.Endpoints))
		for i, domainItem := range domainEndpoint.Endpoints {
			entityEndpointItems[i] = EndpointItem{
				Identifier: domainItem.Identifier,
				URL:        domainItem.URL,
				Role:       domainItem.Role,
			}
		}
		entityEndpoints[key] = Endpoint{
			Version:   domainEndpoint.Version,
			Endpoints: entityEndpointItems,
		}
	}

	entityCredentials := MapCredential{}
	for domainKey, domainCred := range conn.Credential {
		entityRoles := make([]Role, len(domainCred.Roles))
		for i, domainRole := range domainCred.Roles {
			entityRoles[i] = Role{
				Role:        domainRole.Role,
				PartyID:     domainRole.PartyID,
				CountryCode: domainRole.CountryCode,
			}
		}
		entityCredentials[CredentialKey(domainKey)] = Credential{
			Token: domainCred.Token,
			URL:   domainCred.URL,
			Roles: entityRoles,
		}
	}

	return &Connection{
		CountryCode: conn.CountryCode,
		PartyID:     conn.PartyID,
		Endpoint:    entityEndpoints,
		Credential:  entityCredentials,
	}
}

type MapCredential map[CredentialKey]Credential
type MapEndpoint map[string]Endpoint

type Credential struct {
	Token string `bson:"token,omitempty" `
	URL   string `bson:"url,omitempty"`
	Roles []Role `bson:"roles,omitempty"`
}

type Endpoint struct {
	Version   string         `bson:"version,omitempty"`
	Endpoints []EndpointItem `bson:"endpoints,omitempty"`
}

// EndpointItem represents individual endpoint details
type EndpointItem struct {
	Identifier string `bson:"identifier,omitempty"`
	URL        string `bson:"url,omitempty"`
	Role       string `bson:"role,omitempty"`
}

type Role struct {
	Role        string `bson:"role"`
	PartyID     string `bson:"party_id"`
	CountryCode string `bson:"country_code"`
}
