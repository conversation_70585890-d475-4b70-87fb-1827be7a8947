package mongodb

import (
	"context"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type ConnectionRepository struct {
	cfg *config.Config
	db  *mongo.Database
}

func (c *ConnectionRepository) GetConnectionDetails(ctx context.Context, countryCode, partyCode string) (*Connection, error) {
	filter := bson.D{
		{Key: "party_code", Value: bson.D{{Key: "$regex", Value: partyCode}, {Key: "$options", Value: "i"}}},
		{Key: "country_code", Value: bson.D{{Key: "$regex", Value: countryCode}, {Key: "$options", Value: "i"}}},
	}
	result := &Connection{}
	if err := c.db.Collection(c.cfg.Mongo.ConnectionCollection).FindOne(ctx, filter).Decode(result); err != nil {
		return nil, err
	}
	return result, nil
}
func NewRepository(cfg *config.Config, db *mongo.Database) *ConnectionRepository {
	return &ConnectionRepository{cfg: cfg, db: db}
}
