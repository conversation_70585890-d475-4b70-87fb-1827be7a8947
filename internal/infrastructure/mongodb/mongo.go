package mongodb

import (
	"context"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

type Database struct {
	client *mongo.Client
	cfg    *config.MongoConfig
}

func NewDatabase(cfg *config.MongoConfig) (*Database, error) {
	mongoClient, err := hubmongo.ConnectV2(&hubmongo.Config{
		Schema:     cfg.Schema,
		Host:       cfg.Host,
		Port:       cfg.Port,
		Username:   cfg.Username,
		Password:   cfg.Password,
		AuthSource: cfg.AuthSource,
		Options:    cfg.Options,
		Timeout:    cfg.Timeout,
	})
	if err != nil {
		return nil, err
	}
	log.Info().Str("database", cfg.Database).Msg("connected to MongoDB")
	return &Database{client: mongoClient, cfg: cfg}, nil
}

func (c *Database) Disconnect(ctx context.Context) error {
	log.Info().Msg("disconnecting MongoDB client")
	return c.client.Disconnect(ctx)
}

func (c *Database) connectionsCollection() *mongo.Collection {
	return c.client.Database(c.cfg.Database).Collection(c.cfg.ConnectionCollection)
}

func (c *Database) LoadConnectionRecords() ([]Connection, error) {
	coll := c.connectionsCollection()
	ctx := context.Background()

	var recs []Connection
	cursor, err := coll.Find(ctx, bson.D{})
	if err != nil {
		return nil, err
	}
	if cerr := cursor.All(ctx, &recs); cerr != nil {
		return nil, cerr
	}
	return recs, nil
}
