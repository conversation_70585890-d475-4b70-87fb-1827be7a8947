package subscriber

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/service"
	"github.com/inv-cloud-platform/hub-com-rtd-go/hubrtd"
	"github.com/rs/zerolog/log"
)

type Subscriber struct {
	mgr      *hubrtd.Manager
	consumer *hubrtd.Consumer
	svc      *service.AppService
	ctx      context.Context
}

func NewSubscriber(ctx context.Context, cfg *config.Config, mgr *hubrtd.Manager, svc *service.AppService) (*Subscriber, error) {
	consumer, err := mgr.NewConsumer(ctx, hubrtd.ConsumerOptions{Subjects: cfg.NATS.Subjects, DeliverPolicy: hubrtd.DeliverNewPolicy, Durable: config.AppName})
	if err != nil {
		log.Error().Err(err).Msgf("error occurred while creating new consumer")
		return nil, err
	}
	subscriber := &Subscriber{ctx: ctx, mgr: mgr, consumer: consumer, svc: svc}
	return subscriber, nil
}

func (s *Subscriber) Start() error {
	errChan := make(chan error, 1)
	go func() {
		err := s.consumer.Start(s.handleMsg)
		if err != nil {
			log.Error().Err(err).Msg("error occurred while starting consumer")
			errChan <- err
		}
		close(errChan)
	}()

	select {
	case err := <-errChan:
		return err
	case <-time.After(2 * time.Second):
		log.Info().Msg("consumer started successfully")
		return nil
	}
}

func (s *Subscriber) Stop() {
	if s.mgr != nil {
		s.mgr.Close()
	}
}
func (s *Subscriber) handleMsg(message []byte, subject string) error {
	log.Info().Str("subject", subject).RawJSON("message", message).Msg("reading message")
	sub := strings.ToLower(subject)
	isValidSubject := strings.Contains(sub, strings.ToLower(domain.SyncOcpiObjectEvent)) || strings.Contains(sub, strings.ToLower(domain.OcpiCommandEvent))
	if !isValidSubject {
		msg := fmt.Sprintf("subject is not valid allowed subject should contains [%s, %s]", domain.SyncOcpiObjectEvent, domain.OcpiCommandEvent)
		log.Info().Msg(msg)
		return nil
	}
	return s.svc.ProcessEvent(s.ctx, message, subject)
}
