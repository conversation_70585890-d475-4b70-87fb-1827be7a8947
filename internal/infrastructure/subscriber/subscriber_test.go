package subscriber

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"testing"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/infrastructure/mongodb"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/service"
	"github.com/inv-cloud-platform/fe-ev-csms-io/tests/containers"
	"github.com/inv-cloud-platform/hub-com-auth-go/hubauth"
	"github.com/inv-cloud-platform/hub-com-metrics-go/hubmetrics"
	"github.com/inv-cloud-platform/hub-com-rtd-go/hubrtd"
	"github.com/jarcoal/httpmock"
	"github.com/nats-io/nats.go/jetstream"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"resty.dev/v3"
)

type MockToken struct {
	StartAutoRefreshFunc func(ctx context.Context) error
	AccessTokenFunc      func() string
}

func (m *MockToken) StartAutoRefresh(ctx context.Context) error {
	if m.StartAutoRefreshFunc != nil {
		return m.StartAutoRefreshFunc(ctx)
	}
	return nil
}

func (m *MockToken) AccessToken() string {
	if m.AccessTokenFunc != nil {
		return m.AccessTokenFunc()
	}
	return ""
}

type SubscriberSuite struct {
	suite.Suite
	ctx            context.Context
	cfg            *config.Config
	mongoContainer *containers.MongoContainer
	mongoClient    *mongo.Client
	mongoDatabase  *mongo.Database
	restyClient    *resty.Client

	mgr           *hubrtd.Manager
	natsContainer *containers.NATSContainer
	js            jetstream.JetStream
	publisher     *hubrtd.Publisher
	natsURI       string
	appSrv        *service.AppService
	token         hubauth.Token
	subs          *Subscriber
}

func (suite *SubscriberSuite) setupMongoContainer() {
	mongoContainer := containers.NewMongoContainer()
	err := mongoContainer.RunContainer()
	suite.Require().Nil(err)
	suite.mongoContainer = mongoContainer
	suite.mongoClient = suite.mongoContainer.GetClient()
	suite.mongoDatabase = suite.mongoClient.Database(suite.cfg.Mongo.Database)
}

func (suite *SubscriberSuite) setupNats() {
	natsContainer := containers.NewNatsContainer()
	err := natsContainer.InitContainer()
	suite.Require().Nil(err)
	suite.natsContainer = natsContainer
	for _, subject := range suite.cfg.NATS.Subjects {
		js, errStream := natsContainer.CreateStream(suite.cfg.NATS.StreamName, subject)
		suite.Require().Nil(errStream)
		suite.Require().NotNil(js)
	}

	suite.natsURI = natsContainer.ServerURL()
}

func (suite *SubscriberSuite) SetupSuite() {
	cfg, err := config.NewConfig()
	suite.Require().Nil(err)
	ctx := context.Background()
	httpmock.Activate()
	suite.restyClient = resty.New()
	httpmock.ActivateNonDefault(suite.restyClient.Client())
	suite.ctx = ctx
	suite.cfg = cfg
	res, err := hubmetrics.NewReporter("fe-ev-csms-io")
	suite.Require().Nil(err)
	suite.Require().NotNil(res)
	suite.setupNats()
	suite.setupMongoContainer()
	repo := mongodb.NewRepository(cfg, suite.mongoDatabase)
	suite.appSrv = service.NewAppService(cfg, suite.restyClient, service.NewConnectionService(cfg, repo), &MockToken{})
	suite.cfg.NATS.URL = suite.natsURI
	mgr, errMrg := common.SetupNats(suite.ctx, suite.cfg.NATS)
	suite.Require().Nil(errMrg)
	suite.mgr = mgr
	suite.subs, err = NewSubscriber(suite.ctx, suite.cfg, suite.mgr, suite.appSrv)
	suite.Require().Nil(err)
	errStart := suite.subs.Start()
	suite.Require().Nil(errStart)
}

func (_ *SubscriberSuite) SetupTest() {
	httpmock.Reset()
}

func (suite *SubscriberSuite) TearDownSuite() {
	httpmock.DeactivateAndReset()
	errDisconnect := suite.mongoContainer.GetClient().Disconnect(suite.ctx)
	suite.Require().Nil(errDisconnect)
	suite.subs.Stop()
	suite.mongoContainer.DeleteContainer()
	suite.NoError(errDisconnect)
}
func TestSubscriberSuite(t *testing.T) {
	suite.Run(t, new(SubscriberSuite))
}

func (*SubscriberSuite) readTestFileData(fileName string) ([]byte, error) {
	data, err := os.ReadFile(path.Join("../../../", "./tests/data", fileName))
	if err != nil {
		return nil, err
	}
	return data, nil
}
func (suite *SubscriberSuite) saveConnection() {
	connection, err := suite.readTestFileData("connection_data.json")
	suite.Require().Nil(err)
	suite.Require().NotNil(connection)
	connectionReq := &domain.Connection{}
	errUnmarshal := json.Unmarshal(connection, connectionReq)
	suite.Require().Nil(errUnmarshal)
	dbRequest := &mongodb.Connection{}
	dbRequest = dbRequest.ToEntityConnection(connectionReq)
	res, errInsert := suite.mongoDatabase.Collection(suite.cfg.Mongo.ConnectionCollection).InsertOne(suite.ctx, dbRequest)
	suite.Require().Nil(errInsert)
	suite.Require().NotNil(res)
}
func (suite *SubscriberSuite) TestOcpiCommandPublish() {
	suite.Run("publish start session success", func() {
		suite.saveConnection()
		reqBody := map[string]string{}
		reqBody["token"] = "http://example.com"
		reqBody["location_id"] = "12121"
		rawReqBody, err := json.Marshal(reqBody)
		suite.Require().Nil(err)
		cmd := domain.Command{
			DeviceID: "device123",
			Payload: domain.Payload{
				CommandType: domain.CommandTypeStartSession,
				RequestBody: rawReqBody,
				TrackingID:  "trackingid-1234",
			},
			TS: time.Now(),
		}
		sessionStart := domain.TelemetryEvent{
			Meta: &domain.Meta{},
			Data: []domain.Command{cmd},
		}
		bMsg, errMarshal := json.Marshal(sessionStart)
		suite.Require().Nil(errMarshal)
		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "ocpiCommand", "us", "dom", "device123")
		msg := &hubrtd.Msg{
			ID:      "test-publish",
			Subject: sub,
			Data:    bMsg,
		}

		response := &domain.CommandResponse{
			StatusCode: 1000,
			Data: domain.CommandResult{
				Result:  "Success",
				Timeout: 70,
			},
		}
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/commands/" + cmd.Payload.CommandType.String()
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("POST", url, responder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "12",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}
		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		_, errPub := suite.mgr.NewPublisher().Publish(suite.ctx, msg)
		suite.Require().Nil(errPub)
		time.Sleep(3 * time.Second)
	})
}
func (suite *SubscriberSuite) TestSyncOcpiObjectPublish() {
	suite.Run("publish sync ocpi object success", func() {
		suite.saveConnection()
		reqBody := map[string]string{}
		reqBody["token"] = "http://example.com"
		reqBody["location_id"] = "12121"
		rawReqBody, err := json.Marshal(reqBody)
		suite.Require().Nil(err)
		cmd := domain.Command{
			Payload: domain.Payload{
				CommandType: domain.CommandTypeStartSession,
				RequestBody: rawReqBody,
				TrackingID:  "trackingid-1234",
			},
		}
		sessionStart := domain.SyncModuleEvent{
			Meta: domain.MetaSyncModuleEvent{
				Timestamp: 121210,
				TxID:      "1212",
			},
			Data: []domain.SyncModuleEventItem{{
				DeviceID: "device123",
				Payload: domain.SyncModulePayload{
					OcpiObjectType: "locations",
					TrackingID:     "wewewew",
				},
			}},
		}
		bMsg, errMarshal := json.Marshal(sessionStart)
		suite.Require().Nil(errMarshal)
		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "syncOcpiObject", "01JARDB1T6SC3MK3DVR8E38VGN", "01JBCATX304YRKFBMFFW2EGPWZ", "device123")
		msg := &hubrtd.Msg{
			ID:      "test-publish",
			Subject: sub,
			Data:    bMsg,
		}

		response := &domain.CommandResponse{
			StatusCode: 1000,
			Data: domain.CommandResult{
				Result:  "Success",
				Timeout: 70,
			},
		}
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/commands/" + cmd.Payload.CommandType.String()
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("POST", url, responder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "12",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}
		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		dataLoc := map[string]any{}
		mpLocItem := map[string]string{}
		mpLocItem["id"] = "3"
		dataLoc["data"] = mpLocItem
		locURL := suite.cfg.HTTP.LocationHost + "/ocpi/us/dom/emsp/2.2.1/locations/us/gvr/10"
		bLoc, errLoc := json.Marshal(dataLoc)
		suite.Require().Nil(errLoc)

		locResponder := httpmock.NewStringResponder(200, string(bLoc))
		httpmock.RegisterResponder("GET", locURL, locResponder)

		_, errPub := suite.mgr.NewPublisher().Publish(suite.ctx, msg)
		suite.Require().Nil(errPub)
		time.Sleep(3 * time.Second)
	})
}

func (suite *SubscriberSuite) TestWithWrongSubject() {
	suite.Run("test with wrong subject", func() {
		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "otherCommand", "01JARDB1T6SC3MK3DVR8E38VGN", "01JBCATX304YRKFBMFFW2EGPWZ", "device123")
		msg := &hubrtd.Msg{
			ID:      "test-publish",
			Subject: sub,
		}
		_, errPub := suite.mgr.NewPublisher().Publish(suite.ctx, msg)
		suite.Require().Nil(errPub)
		time.Sleep(3 * time.Second)
	})
}
