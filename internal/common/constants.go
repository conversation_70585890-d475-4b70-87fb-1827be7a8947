package common

import (
	"fmt"
)

type ContextKey string

const (
	HeaderContentType         = "Content-Type"
	HeaderAuthorization       = "Authorization"
	HeaderOCPIToPartyID       = "OCPI-to-party-id"
	HeaderOCPIToCountryCode   = "OCPI-to-country-code"
	HeaderOCPIFromPartyID     = "OCPI-from-party-id"
	HeaderOCPIFromCountryCode = "OCPI-from-country-code"
)

const ContentTypeJSON = "application/json"

const (
	CommandCancelReservation = "CANCEL_RESERVATION"
	CommandReserveNow        = "RESERVE_NOW"
	CommandStartSession      = "START_SESSION"
	CommandStopSession       = "STOP_SESSION"
	CommandUnlockConnector   = "UNLOCK_CONNECTOR"
)

func BuildCommandEndpoint(baseURL, command string) string {
	return fmt.Sprintf("%s/%s", baseURL, command)
}
