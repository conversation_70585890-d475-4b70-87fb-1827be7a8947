package common

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/stretchr/testify/assert"
)

func TestMetricsEndpoint(t *testing.T) {
	t.Run("custom metrics are properly defined", func(t *testing.T) {
		// Test that our custom metrics are properly registered
		assert.NotNil(t, ErrorCounter)
		assert.NotNil(t, RestyRequestDuration)
		assert.NotNil(t, RestyRequestErrors)

		// Test incrementing error counter (just verify it doesn't panic)
		ErrorCounter.Inc()

		// Test recording request duration
		RestyRequestDuration.WithLabelValues("test-endpoint", "200").Observe(0.1)

		// Test recording request error
		RestyRequestErrors.WithLabelValues("test-endpoint", "timeout").Inc()
	})

	t.Run("prometheus handler works", func(t *testing.T) {
		// Test that the prometheus handler can serve metrics
		req := httptest.NewRequest("GET", "/metrics", nil)
		w := httptest.NewRecorder()

		promhttp.Handler().ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		body := w.Body.String()

		// Check that it contains expected metrics format
		assert.Contains(t, body, "# HELP")
		assert.Contains(t, body, "# TYPE")

		// Should contain our custom metrics
		assert.Contains(t, body, "fe_ev_csms_io_errors_total")
		assert.Contains(t, body, "appservice_resty_request_duration_seconds")
		assert.Contains(t, body, "appservice_resty_request_errors_total")
	})

	t.Run("port formatting works correctly", func(t *testing.T) {
		// Test the port formatting logic from StartMetricsServer
		port := "9090"
		if !strings.HasPrefix(port, ":") {
			port = ":" + port
		}
		assert.Equal(t, ":9090", port)

		port2 := ":8080"
		if !strings.HasPrefix(port2, ":") {
			port2 = ":" + port2
		}
		assert.Equal(t, ":8080", port2)
	})
}

func TestStartMetricsServerConfig(t *testing.T) {
	t.Run("config validation", func(t *testing.T) {
		cfg := &config.ServiceConfig{
			MetricsServerPort: "9090",
		}

		// Test that config is valid
		assert.NotEmpty(t, cfg.MetricsServerPort)

		// Test port formatting
		port := cfg.MetricsServerPort
		if !strings.HasPrefix(port, ":") {
			port = ":" + port
		}
		assert.Equal(t, ":9090", port)
	})
}
