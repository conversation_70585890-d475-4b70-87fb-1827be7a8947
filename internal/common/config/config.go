package config

import (
	"errors"
	"fmt"
	"time"

	"github.com/caarlos0/env/v6"
	"github.com/rs/zerolog/log"
)

const AppName = "fe-ev-csms-io"

type Config struct {
	Auth    *Auth
	Service *ServiceConfig
	NATS    *NATSConfig
	HTTP    *HTTPConfig
	Logging *LoggingConfig
	Mongo   *MongoConfig
}

type Auth struct {
	KeycloakHost    string `env:"KC_HOST" envDefault:"https://auth.hub-dev.invenco.com"`
	KeycloakRealm   string `env:"KC_REALM" envDefault:"invenco-hub"`
	ClientID        string `env:"AUTH_CLIENT_ID" envDefault:"invenco-client"`
	ClientSecret    string `env:"AUTH_CLIENT_SECRET" envDefault:""`
	ServiceUsername string `env:"AUTH_SERVICE_USERNAME" envDefault:""`
	ServicePassword string `env:"AUTH_SERVICE_PASSWORD" envDefault:""`
}

type ServiceConfig struct {
	Name                 string        `env:"SERVICE_NAME"          envDefault:"fe-ev-csms-io"`
	MetricsServerPort    string        `env:"METRICS_SERVER_PORT" envDefault:"9090"`
	ShutdownTimeout      time.Duration `env:"SHUTDOWN_TIMEOUT"      envDefault:"30s"`
	ReadHeaderTimeout    time.Duration `env:"READ_HEADER_TIMEOUT"   envDefault:"30s"`
	CommandResultTimeout time.Duration `env:"COMMAND_RESULT_TIMEOUT" envDefault:"30s"`
	NoOfDays             int           `env:"NO_OF_DAYS" envDefault:"7"`
}

type NATSConfig struct {
	URL                      string        `env:"NATS_URL"                    envDefault:"nats://localhost:4222"`
	Subjects                 []string      `env:"NATS_SUBJECTS"               envDefault:"dp-ucc-telem-in.eds-ev.>" envSeparator:","`
	QueueGroup               string        `env:"NATS_QUEUE_GROUP"            envDefault:"ocpi-processor"`
	MaxReconnects            int           `env:"NATS_MAX_RECONNECTS"         envDefault:"5"`
	ReconnectWait            time.Duration `env:"NATS_RECONNECT_WAIT"         envDefault:"5s"`
	ConnectTimeout           time.Duration `env:"NATS_CONNECT_TIMEOUT"        envDefault:"10s"`
	MessageProcessingTimeout time.Duration `env:"NATS_MSG_PROCESSING_TIMEOUT" envDefault:"30s"`
	PublicNKey               string        `env:"NATS_NKEY_PUBLIC"`
	PrivateNKey              string        `env:"NATS_NKEY_PRIVATE"`
	AuthEnabled              bool          `env:"NATS_AUTH_ENABLED" envDefault:"false"`
	StreamName               string        `env:"NATS_STREAM" envDefault:"dp-ucc-telem-in"`
	ClientName               string        `env:"NATS_CLIENT_NAME" envDefault:"fe-ev-csms-io"`
}

type HTTPConfig struct {
	Timeout      time.Duration `env:"HTTP_TIMEOUT" envDefault:"15s"`
	Retry        *RetryConfig
	UCCHost      string `env:"HTTP_UCC_HOST" envDefault:"http://localhost:8080"`
	LocationHost string `env:"HTTP_LOCATION_HOST" envDefault:"http://localhost:8080"`
	EvseHost     string `env:"HTTP_EVSE_HOST" envDefault:"http://localhost:8080"`
	TariffHost   string `env:"HTTP_TARIFF_HOST" envDefault:"http://localhost:8080"`
	SessionHost  string `env:"HTTP_SESSION_HOST" envDefault:"http://localhost:8080"`
	CDRHost      string `env:"HTTP_CDR_HOST" envDefault:"http://localhost:8080"`
}

type RetryConfig struct {
	MaxAttempts         int           `env:"HTTP_RETRY_MAX_ATTEMPTS"        envDefault:"3"`
	InitialInterval     time.Duration `env:"HTTP_RETRY_INITIAL_INTERVAL"    envDefault:"1s"`
	MaxInterval         time.Duration `env:"HTTP_RETRY_MAX_INTERVAL"        envDefault:"15s"`
	Multiplier          float64       `env:"HTTP_RETRY_MULTIPLIER"          envDefault:"2.0"`
	RandomizationFactor float64       `env:"HTTP_RETRY_RANDOMIZATION_FACTOR" envDefault:"0.1"`
}

type LoggingConfig struct {
	Level            string `env:"LOG_LEVEL"            envDefault:"info"`
	Format           string `env:"LOG_FORMAT"           envDefault:"json"`
	EnableCaller     bool   `env:"LOG_ENABLE_CALLER"    envDefault:"true"`
	EnableStacktrace bool   `env:"LOG_ENABLE_STACKTRACE" envDefault:"false"`
}

type MongoConfig struct {
	Schema               string        `env:"MONGO_SCHEMA" envDefault:"mongodb"`
	Host                 string        `env:"MONGO_HOST" envDefault:"localhost" `
	Port                 int           `env:"MONGO_PORT" envDefault:"27017"`
	Username             string        `env:"MONGO_USERNAME" envDefault:"root"`
	Password             string        `env:"MONGO_PASSWORD" envDefault:"example"`
	AuthSource           string        `env:"MONGO_AUTH_SOURCE" envDefault:""`
	Database             string        `env:"MONGO_DATABASE" envDefault:"template"`
	Options              string        `env:"MONGO_OPTIONS" envDefault:""`
	Timeout              time.Duration `env:"MONGO_TIMEOUT" envDefault:"30s"`
	ConnectionCollection string        `env:"MONGO_CONNECTION_COLLECTION" envDefault:"connections"`
}

func NewConfig() (*Config, error) {
	cfg := &Config{
		Auth:    &Auth{},
		Service: &ServiceConfig{},
		NATS:    &NATSConfig{},
		HTTP: &HTTPConfig{
			Retry: &RetryConfig{},
		},
		Logging: &LoggingConfig{},
		Mongo:   &MongoConfig{},
	}
	if err := env.Parse(cfg); err != nil {
		return nil, fmt.Errorf("failed to parse environment variables: %w", err)
	}
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}
	log.Debug().Msg("configuration loaded")
	return cfg, nil
}

func (c *Config) Validate() error {
	if c.HTTP.Timeout <= 0 {
		return errors.New("HTTP timeout must be greater than zero")
	}
	if c.HTTP.Retry.MaxAttempts < 1 {
		return errors.New("retry max attempts must be at least 1")
	}
	return nil
}
