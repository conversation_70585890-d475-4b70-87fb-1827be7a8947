package common

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	tallyProm "github.com/uber-go/tally/v4/prometheus"
)

var (
	ErrorCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "fe_ev_csms_io_errors_total",
			Help: "Total number of errors",
		})

	RestyRequestDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "appservice_resty_request_duration_seconds",
		Help:    "Duration of resty requests in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"endpoint", "status"})

	RestyRequestErrors = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "appservice_resty_request_errors_total",
		Help: "Total number of errors in resty requests",
	}, []string{"endpoint", "error_type"})
)

func StartMetricsServer(appConfig *config.ServiceConfig, readTimeout time.Duration, reporter tallyProm.Reporter) error {
	port := appConfig.MetricsServerPort

	if !strings.HasPrefix(port, ":") {
		port = ":" + port
	}
	mux := http.NewServeMux()
	mux.Handle("/metrics", reporter.HTTPHandler())
	server := http.Server{
		Addr:              port,
		Handler:           mux,
		ReadHeaderTimeout: readTimeout,
	}

	// Channel to listen for interrupt or terminate signals
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)

	// Channel to signal that the server has shut down
	done := make(chan error, 1)

	go func() {
		done <- server.ListenAndServe()
	}()

	select {
	case <-stop:
		// Received an interrupt signal, shut down gracefully
		ctx, cancel := context.WithTimeout(context.Background(), appConfig.ShutdownTimeout)
		defer cancel()
		return server.Shutdown(ctx)
	case err := <-done:
		// Server stopped unexpectedly
		return err
	}
}
