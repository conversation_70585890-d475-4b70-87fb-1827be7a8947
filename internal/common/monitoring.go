package common

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	tallyProm "github.com/uber-go/tally/v4/prometheus"
)

var (
	ErrorCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "fe_ev_csms_io_errors_total",
			Help: "Total number of errors",
		})

	RestyRequestDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "appservice_resty_request_duration_seconds",
		Help:    "Duration of resty requests in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"endpoint", "status"})

	RestyRequestErrors = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "appservice_resty_request_errors_total",
		Help: "Total number of errors in resty requests",
	}, []string{"endpoint", "error_type"})
)

func StartMetricsServer(appConfig *config.ServiceConfig, readTimeout time.Duration, reporter tallyProm.Reporter) error {
	port := appConfig.MetricsServerPort

	if !strings.HasPrefix(port, ":") {
		port = ":" + port
	}
	mux := http.NewServeMux()

	mux.HandleFunc("/metrics", func(w http.ResponseWriter, r *http.Request) {
		// Initialize vector metrics so they appear in output
		RestyRequestDuration.WithLabelValues("init", "init").Observe(0)
		RestyRequestErrors.WithLabelValues("init", "init").Add(0)
		
		recorder1 := httptest.NewRecorder()
		promhttp.Handler().ServeHTTP(recorder1, r)

		for k, v := range recorder1.Header() {
			w.Header()[k] = v
		}
		w.WriteHeader(recorder1.Code)

		recorder2 := httptest.NewRecorder()
		reporter.HTTPHandler().ServeHTTP(recorder2, r)

		_, _ = w.Write(recorder1.Body.Bytes())
		hubMetrics := recorder2.Body.String()
		if hubMetrics != "" {
			_, _ = w.Write([]byte("\n# Hub Metrics\n"))
			_, _ = w.Write([]byte(hubMetrics))
		} else {
			_, _ = w.Write([]byte("\n# No Hub Metrics Available\n"))
		}
	})

	server := http.Server{
		Addr:              port,
		Handler:           mux,
		ReadHeaderTimeout: readTimeout,
	}

	return server.ListenAndServe()
}
