package common

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	tallyProm "github.com/uber-go/tally/v4/prometheus"
)

var (
	ErrorCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "fe_ev_csms_io_errors_total",
			Help: "Total number of errors",
		})

	RestyRequestDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "appservice_resty_request_duration_seconds",
		Help:    "Duration of resty requests in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"endpoint", "status"})

	RestyRequestErrors = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "appservice_resty_request_errors_total",
		Help: "Total number of errors in resty requests",
	}, []string{"endpoint", "error_type"})
)

func StartMetricsServer(appConfig *config.ServiceConfig, readTimeout time.Duration, reporter tallyProm.Reporter) error {
	port := appConfig.MetricsServerPort

	if !strings.HasPrefix(port, ":") {
		port = ":" + port
	}
	mux := http.NewServeMux()

	// Create a handler that combines both hubmetrics and custom metrics
	mux.HandleFunc("/metrics", func(w http.ResponseWriter, r *http.Request) {
		// Set headers to ensure browser displays content properly
		w.Header().Set("Content-Type", "text/plain; charset=utf-8")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("X-Content-Type-Options", "nosniff")

		// Get the default Prometheus metrics (including our custom metrics)
		recorder1 := httptest.NewRecorder()
		promhttp.Handler().ServeHTTP(recorder1, r)
		defaultMetrics := recorder1.Body.String()

		// Get the hubmetrics data
		recorder2 := httptest.NewRecorder()
		reporter.HTTPHandler().ServeHTTP(recorder2, r)
		hubMetrics := recorder2.Body.String()

		// Combine both metric outputs
		var combinedMetrics bytes.Buffer
		combinedMetrics.WriteString(defaultMetrics)

		// Only add hubmetrics if it's not empty and doesn't duplicate default metrics
		if hubMetrics != "" && hubMetrics != defaultMetrics {
			combinedMetrics.WriteString("\n")
			combinedMetrics.WriteString(hubMetrics)
		}

		// Write the combined metrics
		w.WriteHeader(http.StatusOK)
		_, _ = io.Copy(w, &combinedMetrics)
	})

	// Add a simple browser-friendly metrics viewer
	mux.HandleFunc("/metrics-view", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.WriteHeader(http.StatusOK)

		html := `<!DOCTYPE html>
<html>
<head>
    <title>Metrics Viewer</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .header { margin-bottom: 20px; }
        iframe { width: 100%; height: 80vh; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Prometheus Metrics</h1>
        <p>Raw metrics endpoint: <a href="/metrics" target="_blank">/metrics</a></p>
    </div>
    <iframe src="/metrics"></iframe>
</body>
</html>`
		_, _ = w.Write([]byte(html))
	})

	server := http.Server{
		Addr:              port,
		Handler:           mux,
		ReadHeaderTimeout: readTimeout,
	}

	// Channel to listen for interrupt or terminate signals
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)

	// Channel to signal that the server has shut down
	done := make(chan error, 1)

	go func() {
		done <- server.ListenAndServe()
	}()

	select {
	case <-stop:
		// Received an interrupt signal, shut down gracefully
		ctx, cancel := context.WithTimeout(context.Background(), appConfig.ShutdownTimeout)
		defer cancel()
		return server.Shutdown(ctx)
	case err := <-done:
		// Server stopped unexpectedly
		return err
	}
}
