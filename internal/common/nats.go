package common

import (
	"context"
	"time"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/hub-com-rtd-go/hubrtd"
	"github.com/nats-io/nats.go"
	"github.com/nats-io/nkeys"
	"github.com/rs/zerolog/log"
)

func SetupNats(ctx context.Context, cfg *config.NATSConfig) (*hubrtd.Manager, error) {
	options := []nats.Option{
		nats.RetryOnFailedConnect(true),
		nats.MaxReconnects(-1),
		nats.ReconnectWait(time.Second),
		nats.ConnectHandler(func(_ *nats.Conn) {
			log.Info().Msg("nats: connected")
		}),
		nats.ErrorHandler(func(_ *nats.Conn, _ *nats.Subscription, err error) {
			log.Error().Err(err).Msg("nats: an asyc error happened")
		}),
		nats.DisconnectErrHandler(func(_ *nats.Conn, err error) {
			log.Warn().Err(err).Msg("nats: disconnected")
		}),
		nats.ReconnectHandler(func(_ *nats.Conn) {
			log.Info().Msg("nats: reconnected")
		}),
		nats.ClosedHandler(func(nc *nats.Conn) {
			log.Error().Err(nc.LastError()).Msg("nats: connection closed")
		}),
	}
	if cfg.AuthEnabled {
		signing, errNkeySeed := nkeys.FromSeed([]byte(cfg.PrivateNKey))
		if errNkeySeed != nil {
			return nil, errNkeySeed
		}
		options = append(options, nats.Nkey(cfg.PublicNKey, signing.Sign))
	}
	return hubrtd.NewManager(
		ctx,
		hubrtd.ManagerConfig{
			URL:        cfg.URL,
			ClientName: cfg.ClientName,
			StreamName: cfg.StreamName,
		},
		options...,
	)
}
