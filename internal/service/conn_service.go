package service

import (
	"context"

	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/infrastructure/mongodb"
)

type ConnectionService struct {
	cfg   *config.Config
	repo  *mongodb.ConnectionRepository
	store IStore
}

func (c *ConnectionService) GetConnectionDetails(ctx context.Context, countryCode, partyCode string) (*domain.Connection, error) {
	key := ConnectionKey + partyCode + countryCode
	storedData, ok := c.store.Get(key)
	if ok {
		return storedData.(*domain.Connection), nil
	}
	res, err := c.repo.GetConnectionDetails(ctx, countryCode, partyCode)
	if err != nil {
		return nil, err
	}
	data := res.ToDomainConnection()
	c.store.Set(key, data)
	return data, nil
}

func NewConnectionService(cfg *config.Config, repo *mongodb.ConnectionRepository) *ConnectionService {
	return &ConnectionService{
		cfg:   cfg,
		repo:  repo,
		store: NewStore(),
	}
}
