package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"testing"
	"time"

	"github.com/inv-cloud-platform/fe-ev-com-ocpi-go/ocpi"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/infrastructure/mongodb"
	"github.com/inv-cloud-platform/fe-ev-csms-io/tests/containers"
	"github.com/inv-cloud-platform/hub-com-auth-go/hubauth"
	"github.com/inv-cloud-platform/hub-com-metrics-go/hubmetrics"
	"github.com/inv-cloud-platform/hub-com-rtd-go/hubrtd"
	"github.com/jarcoal/httpmock"
	"github.com/nats-io/nats.go/jetstream"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go/modules/nats"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"resty.dev/v3"
)

type MockToken struct {
	StartAutoRefreshFunc func(ctx context.Context) error
	AccessTokenFunc      func() string
}

func (m *MockToken) StartAutoRefresh(ctx context.Context) error {
	if m.StartAutoRefreshFunc != nil {
		return m.StartAutoRefreshFunc(ctx)
	}
	return nil
}

func (m *MockToken) AccessToken() string {
	if m.AccessTokenFunc != nil {
		return m.AccessTokenFunc()
	}
	return ""
}

type ServiceSuite struct {
	suite.Suite
	ctx            context.Context
	cfg            *config.Config
	mongoContainer *containers.MongoContainer
	mongoClient    *mongo.Client
	mongoDatabase  *mongo.Database
	restyClient    *resty.Client

	mgr           *hubrtd.Manager
	natsContainer *nats.NATSContainer
	js            jetstream.JetStream
	publisher     *hubrtd.Publisher
	natsURI       string
	appSrv        *AppService
	token         hubauth.Token
}

func (suite *ServiceSuite) setupMongoContainer() {
	mongoContainer := containers.NewMongoContainer()
	err := mongoContainer.RunContainer()
	if err != nil {
		log.Fatal().Msgf("failed to initialize mongo container: %v", err)
	}
	suite.mongoContainer = mongoContainer
	suite.mongoClient = suite.mongoContainer.GetClient()
	suite.mongoDatabase = suite.mongoClient.Database(suite.cfg.Mongo.Database)
}

func (suite *ServiceSuite) setupNats() {
	natsContainer := containers.NewNatsContainer()
	err := natsContainer.InitContainer()
	suite.Require().Nil(err)
	suite.natsURI = natsContainer.ServerURL()
}

func (suite *ServiceSuite) SetupSuite() {
	cfg, err := config.NewConfig()
	suite.Require().Nil(err)
	ctx := context.Background()
	httpmock.Activate()
	suite.restyClient = resty.New()
	httpmock.ActivateNonDefault(suite.restyClient.Client())
	suite.ctx = ctx
	suite.cfg = cfg
	res, err := hubmetrics.NewReporter("fe-ev-csms-io")
	suite.Require().Nil(err)
	suite.Require().NotNil(res)
	suite.setupNats()
	suite.setupMongoContainer()
	repo := mongodb.NewRepository(cfg, suite.mongoDatabase)
	suite.appSrv = NewAppService(cfg, suite.restyClient, NewConnectionService(cfg, repo), &MockToken{})
}

func (_ *ServiceSuite) SetupTest() {
	httpmock.Reset()
}

func (suite *ServiceSuite) TearDownSuite() {
	httpmock.DeactivateAndReset()
	errDisconnect := suite.mongoContainer.GetClient().Disconnect(suite.ctx)
	suite.mongoContainer.DeleteContainer()
	suite.NoError(errDisconnect)
}
func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(ServiceSuite))
}

func (suite *ServiceSuite) TestExtractConnectionInformation() {
	// Subject: fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", collectionName, companyId, siteId, event.DeviceID),
	suite.Run("extract information with blank subject", func() {
		res, err := suite.appSrv.getDeviceID("")
		suite.Require().Empty(res)
		suite.Require().NotNil(err)
		suite.Error(domain.ErrSubjectIsNotValid, err)
	})
	suite.Run("subject with all information", func() {
		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "locations", "us", "dom", "device123")
		res, err := suite.appSrv.getDeviceID(sub)
		suite.Require().NotEmpty(res)
		suite.Require().Nil(err)
	})
}

func (suite *ServiceSuite) TestGetCommandEndpoint() {
	url := suite.appSrv.getCommandEndpoint("http://example.com", domain.CommandTypeCancelReservation)
	suite.Run("cancel reservation url", func() {
		suite.Equal("http://example.com/CANCEL_RESERVATION", url)
	})
	url = suite.appSrv.getCommandEndpoint("http://example.com", domain.CommandTypeStartSession)
	suite.Run("cancel reservation url", func() {
		suite.Equal("http://example.com/START_SESSION", url)
	})
}

func (_ *ServiceSuite) readTestFileData(fileName string) ([]byte, error) {
	data, err := os.ReadFile(path.Join("../../", "./tests/data", fileName))
	if err != nil {
		return nil, err
	}
	return data, nil
}
func (suite *ServiceSuite) TestGetRequestBody() {
	suite.Run("get request body error ", func() {
		cmd := domain.Command{}
		emspInfo := &domain.EMSPInformation{}
		rBody, err := suite.appSrv.getRequestBody(cmd, emspInfo)
		suite.Require().Nil(rBody)
		suite.Require().NotNil(err)
	})
	suite.Run("get request body success ", func() {
		reqBody := map[string]string{}
		reqBody["token"] = "http://example.com"
		reqBody["location_id"] = "12121"
		rawReqBody, err := json.Marshal(reqBody)
		suite.Require().Nil(err)
		cmd := domain.Command{
			DeviceID: "device123",
			Payload: domain.Payload{
				CommandType: domain.CommandTypeStartSession,
				RequestBody: rawReqBody,
				TrackingID:  "us-dom-START_SESSION-US-GVR-8-63-00001",
			},
			TS: time.Now(),
		}
		emspInfo := &domain.EMSPInformation{
			URL:         "http:example.com",
			Version:     "2.2.1",
			PartyID:     "dom",
			CountryCode: "us",
		}
		rBody, err := suite.appSrv.getRequestBody(cmd, emspInfo)
		suite.Require().NotNil(rBody)
		suite.Require().Nil(err)
	})
}

func (suite *ServiceSuite) saveConnection() {
	connection, err := suite.readTestFileData("connection_data.json")
	suite.Require().Nil(err)
	suite.Require().NotNil(connection)
	connectionReq := &domain.Connection{}
	errUnmarshal := json.Unmarshal(connection, connectionReq)
	suite.Require().Nil(errUnmarshal)
	dbRequest := &mongodb.Connection{}
	dbRequest = dbRequest.ToEntityConnection(connectionReq)
	res, errInsert := suite.mongoDatabase.Collection(suite.cfg.Mongo.ConnectionCollection).InsertOne(suite.ctx, dbRequest)
	suite.Require().Nil(errInsert)
	suite.Require().NotNil(res)
}

func (suite *ServiceSuite) cdrsResponse() *ocpi.CdrsResponse {
	fileContent, err := suite.readTestFileData("cdrs_response.json")
	suite.Require().Nil(err)
	suite.Require().NotNil(fileContent)
	res := &ocpi.CdrsResponse{}
	errUnmarshal := json.Unmarshal(fileContent, res)
	suite.Require().Nil(errUnmarshal)
	return res
}
func (suite *ServiceSuite) locationResponse() *ocpi.LocationResponse {
	fileContent, err := suite.readTestFileData("location_response.json")
	suite.Require().Nil(err)
	suite.Require().NotNil(fileContent)
	res := &ocpi.LocationResponse{}
	errUnmarshal := json.Unmarshal(fileContent, res)
	suite.Require().Nil(errUnmarshal)
	return res
}
func (suite *ServiceSuite) tariffResponse() *ocpi.TariffsResponse {
	fileContent, err := suite.readTestFileData("tariffs_response.json")
	suite.Require().Nil(err)
	suite.Require().NotNil(fileContent)
	res := &ocpi.TariffsResponse{}
	errUnmarshal := json.Unmarshal(fileContent, res)
	suite.Require().Nil(errUnmarshal)
	return res
}
func (suite *ServiceSuite) TestPostCommand() {
	suite.Run("post command success", func() {
		reqBody := map[string]string{}
		reqBody["token"] = "http://example.com"
		reqBody["location_id"] = "12121"
		rawReqBody, err := json.Marshal(reqBody)
		suite.Require().Nil(err)
		cmd := domain.Command{
			DeviceID: "device123",
			Payload: domain.Payload{
				CommandType: domain.CommandTypeStartSession,
				RequestBody: rawReqBody,
				TrackingID:  "us-dom-START_SESSION-US-GVR-8-63-00001",
			},
			TS: time.Now(),
		}
		suite.saveConnection()
		event := &domain.TelemetryEvent{
			Meta: &domain.Meta{},
			Data: []domain.Command{cmd},
		}
		msg, err := json.Marshal(event)
		suite.Require().NoError(err)
		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "ocpiCommand", "us", "dom", "device123")
		//dp-ucc-telem-in.eds-ev.ocpiCommand.01JMFWTA7Q29D4PZXRE81JB83Y.01JMFXCGC1C146SDHC2PDSRM0T.74f4345ed5731d46189ef8e9e20acaa7
		response := &domain.CommandResponse{
			StatusCode: 1000,
			Data: domain.CommandResult{
				Result:  "Success",
				Timeout: 70,
			},
		}
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/commands/" + cmd.Payload.CommandType.String()
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("POST", url, responder)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "63",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}, {
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "64",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}
		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)
		errPost := suite.appSrv.ProcessEvent(suite.ctx, msg, sub)
		suite.Require().Nil(errPost)
	})
}

func (suite *ServiceSuite) TestLocationSyncOcpiObject() {
	suite.Run("sync ocpi location success", func() {
		cmd := domain.SyncModuleEventItem{
			DeviceID: "device2323223",
			Payload: domain.SyncModulePayload{
				OcpiObjectType: "locations",
				TrackingID:     "Eds-ev-2323232",
			},
			TS: time.Now(),
		}
		suite.saveConnection()
		event := &domain.SyncModuleEvent{
			Meta: domain.MetaSyncModuleEvent{},
			Data: []domain.SyncModuleEventItem{cmd},
		}
		msg, err := json.Marshal(event)
		suite.Require().NoError(err)

		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "syncOcpiObject", "us", "dom", "device123")
		response := suite.locationResponse()
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/locations/10"
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("GET", url, responder)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "63",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}, {
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "64",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}
		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)
		errPost := suite.appSrv.ProcessEvent(suite.ctx, msg, sub)
		suite.Require().Nil(errPost)
	})
}

func (suite *ServiceSuite) TestCdrSyncOcpiObject() {
	suite.saveConnection()
	suite.Run("sync ocpi success", func() {
		cmd := domain.SyncModuleEventItem{
			DeviceID: "device2323223",
			Payload: domain.SyncModulePayload{
				OcpiObjectType: domain.Cdrs,
				TrackingID:     "Eds-ev-2323232",
			},
			TS: time.Now(),
		}
		event := &domain.SyncModuleEvent{
			Meta: domain.MetaSyncModuleEvent{},
			Data: []domain.SyncModuleEventItem{cmd},
		}
		msg, err := json.Marshal(event)
		suite.Require().NoError(err)

		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "syncOcpiObject", "us", "dom", "device123")
		response := suite.cdrsResponse()
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/cdrs"
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("GET", url, responder)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "15",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}, {
			ChargerID: "us/gvr/10",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "64",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}
		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)
		errPost := suite.appSrv.ProcessEvent(suite.ctx, msg, sub)
		suite.Require().Nil(errPost)
	})
}

func (suite *ServiceSuite) TestTariffSyncOcpiObject() {
	suite.saveConnection()
	suite.Run("sync ocpi tariffs success", func() {
		cmd := domain.SyncModuleEventItem{
			DeviceID: "device2323223",
			Payload: domain.SyncModulePayload{
				OcpiObjectType: domain.Tariffs,
				TrackingID:     "Eds-ev-2323232",
			},
			TS: time.Now(),
		}
		event := &domain.SyncModuleEvent{
			Meta: domain.MetaSyncModuleEvent{},
			Data: []domain.SyncModuleEventItem{cmd},
		}
		msg, err := json.Marshal(event)
		suite.Require().NoError(err)

		sub := fmt.Sprintf("dp-ucc-telem-in.eds-ev.%s.%s.%s.%s", "syncOcpiObject", "us", "dom", "device123")
		response := suite.tariffResponse()
		bData, err := json.Marshal(response)
		suite.Require().Nil(err)
		url := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/tariffs"
		responder := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("GET", url, responder)

		evseData := []domain.EvseData{{
			ChargerID: "us/gvr/8",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "15",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}, {
			ChargerID: "us/gvr/8",
			Connection: domain.EvseConnection{
				CountryCode: "us",
				PartyID:     "dom",
			},
			Environment: "dev",
			EvseUID:     "64",
			Site:        "dummy text",
			UccID:       "some_device_id",
		}}
		evseRes := domain.EvseResponse{
			Data:   evseData,
			Status: true,
		}

		responseLoc := suite.locationResponse()
		bData, errLoc := json.Marshal(responseLoc)
		suite.Require().Nil(errLoc)
		locURL := "https://example.com/externalIncoming/ocpi/cpo/2.2.1/locations/8"
		responderLoc := httpmock.NewStringResponder(200, string(bData))
		httpmock.RegisterResponder("GET", locURL, responderLoc)

		evseURL := suite.cfg.HTTP.EvseHost + "/device/evses/device123"
		bEvse, err := json.Marshal(evseRes)
		suite.Require().Nil(err)

		evseResponder := httpmock.NewStringResponder(200, string(bEvse))
		httpmock.RegisterResponder("GET", evseURL, evseResponder)

		urlUcc := suite.cfg.HTTP.UCCHost + "/operations"
		responderUcc := httpmock.NewStringResponder(200, "")
		httpmock.RegisterResponder("POST", urlUcc, responderUcc)
		errPost := suite.appSrv.ProcessEvent(suite.ctx, msg, sub)
		suite.Require().Nil(errPost)
	})
}

func (suite *ServiceSuite) TestFromAndToDate() {
	url := "https://ocpi.server.com/2.2/tariffs"
	suite.Run("tariff success", func() {
		urlTariff, err := suite.appSrv.fromAndToDate(url)
		suite.Require().Nil(err)
		suite.Require().NotEmpty(urlTariff)
	})

	suite.Run("session success", func() {
		url = "https://ocpi.server.com/2.2/sessions"
		urlSession, err := suite.appSrv.fromAndToDate(url)
		suite.Require().Nil(err)
		suite.Require().NotEmpty(urlSession)
	})
}
