package service

import (
	"sync"
	"time"
)

const (
	SessionKey    = "sessions"
	TariffKey     = "tariffs"
	ConnectionKey = "connections"

	defaultExpiry = 12 * time.Hour
)

type params struct {
	data     map[string]any
	duration time.Duration
	mutex    sync.RWMutex
}

// SetWithExpiry implements IStore.
func (p *params) SetWithExpiry(key string, value any, exp time.Duration) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.data[key] = value
	go p.customExpiry(key, exp)
}

func (p *params) customExpiry(key string, duration time.Duration) {
	time.Sleep(duration)
	p.mutex.Lock()
	defer p.mutex.Unlock()
	delete(p.data, key)
}

// NewStore - instance of storage
func NewStore() IStore {
	return &params{
		data:     make(map[string]any),
		duration: defaultExpiry,
		mutex:    sync.RWMutex{},
	}
}

// Set -
func (p *params) Set(key string, value any) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.data[key] = value
	go p.expireAfterDuration(key)
}

func (p *params) expireAfterDuration(key string) {
	time.Sleep(p.duration)
	p.mutex.Lock()
	defer p.mutex.Unlock()
	delete(p.data, key)
}

func (p *params) Reset() {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.data = make(map[string]any)
}

func (p *params) Delete(key string) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	delete(p.data, key)
}

// Get -
func (p *params) Get(key string) (any, bool) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	value, ok := p.data[key]
	return value, ok
}

// IStore -
type IStore interface {
	Set(key string, value any)
	SetWithExpiry(key string, value any, exp time.Duration)
	Get(key string) (any, bool)
	Reset()
	Delete(key string)
}
