package domain

import (
	"fmt"
	"net/http"
)

const (
	ErrCodeInvalidCommand   = "INVALID_COMMAND"
	ErrCodeValidationFailed = "VALIDATION_FAILED"
	ErrCodeServerError      = "SERVER_ERROR"
)

type Error struct {
	Code       string
	Message    string
	HTTPStatus int
	Err        error
}

func (e *Error) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s: %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *Error) Unwrap() error {
	return e.Err
}

func InvalidCommandError(message string, err error) *Error {
	return &Error{
		Code:       ErrCodeInvalidCommand,
		Message:    message,
		HTTPStatus: http.StatusBadRequest,
		Err:        err,
	}
}

func ValidationError(message string, err error) *Error {
	return &Error{
		Code:       ErrCodeValidationFailed,
		Message:    message,
		HTTPStatus: http.StatusBadRequest,
		Err:        err,
	}
}

func ServerError(message string, err error) *Error {
	return &Error{
		Code:       ErrCodeServerError,
		Message:    message,
		HTTPStatus: http.StatusServiceUnavailable,
		Err:        err,
	}
}
