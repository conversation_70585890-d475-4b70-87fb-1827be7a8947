package domain

import "time"

type TelemetryEvent struct {
	Meta *Meta     `json:"meta"`
	Data []Command `json:"data"`
}

type Meta struct {
	Timestamp int64  `json:"timestamp"`
	TxID      string `json:"txID"`
}

type SubjectInformation struct {
	CountryCode    string `json:"country_code"`
	PartyCode      string `json:"party_code"`
	DeviceID       string `json:"device_id"`
	CollectionName string `json:"collection_name"`
}

const (
	Locations string = "locations"
	Sessions  string = "sessions"
	Tariffs   string = "tariffs"
	Cdrs      string = "cdrs"
)

type SyncModulePayload struct {
	OcpiObjectType string `json:"ocpiObjectType"`
	TrackingID     string `json:"trackingId"`
}

type SyncModuleEvent struct {
	Meta MetaSyncModuleEvent   `json:"meta"`
	Data []SyncModuleEventItem `json:"data"`
}

type SyncModuleEventItem struct {
	DeviceID string            `json:"deviceId"`
	Payload  SyncModulePayload `json:"payload"`
	TS       time.Time         `json:"ts"`
}

type MetaSyncModuleEvent struct {
	Timestamp int64  `json:"timestamp"`
	TxID      string `json:"txID"`
}
