package domain

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

var (
	ErrSubjectIsNotValid = errors.New("subject is not valid")
)

type ConnectionInfo struct {
	CountryCode string `json:"country_code"`
	PartyID     string `json:"party_id"`
}

type Command struct {
	DeviceID string    `json:"deviceId"`
	Payload  Payload   `json:"payload"`
	TS       time.Time `json:"ts"`
}

type Payload struct {
	CommandType CommandType     `json:"command_type"`
	RequestBody json.RawMessage `json:"request_body"`
	TrackingID  string          `json:"trackingId"`
}

type CommandType string

const (
	CommandTypeCancelReservation CommandType = "CANCEL_RESERVATION"
	CommandTypeReserveNow        CommandType = "RESERVE_NOW"
	CommandTypeStartSession      CommandType = "START_SESSION"
	CommandTypeStopSession       CommandType = "STOP_SESSION"
	CommandTypeUnlockConnector   CommandType = "UNLOCK_CONNECTOR"
)

type CommandResponse struct {
	StatusCode    int           `json:"status_code"`
	Data          CommandResult `json:"data"`
	StatusMessage string        `json:"status_message"`
	Timestamp     time.Time     `json:"timestamp"`
}

type LocationResponse struct {
	StatusCode    int       `json:"status_code"`
	Data          any       `json:"data"`
	StatusMessage string    `json:"status_message"`
	Timestamp     time.Time `json:"timestamp"`
}

type CommandResult struct {
	Result  string       `json:"result"`
	Message *DisplayText `json:"message,omitempty"`
	Timeout int          `json:"timeout,omitempty"`
}

type DisplayText struct {
	Language string `json:"language"`
	Text     string `json:"text"`
}

func (c CommandType) IsValid() bool {
	switch c {
	case CommandTypeCancelReservation,
		CommandTypeReserveNow,
		CommandTypeStartSession,
		CommandTypeStopSession,
		CommandTypeUnlockConnector:
		return true
	default:
		return false
	}
}

func (c CommandType) String() string {
	return string(c)
}

func (c *Command) Validate() error {
	if !c.Payload.CommandType.IsValid() {
		return InvalidCommandError(
			"invalid command type",
			fmt.Errorf("command type '%s' is not recognized", c.Payload.CommandType),
		)
	}

	if c.DeviceID == "" {
		return ValidationError(
			"device id is required",
			errors.New("device id must not be empty"),
		)
	}

	if len(c.Payload.RequestBody) == 0 {
		return ValidationError(
			"request body is required",
			errors.New("request body must not be empty"),
		)
	}

	return nil
}
