package domain

import (
	"errors"
	"strings"
)

type CredentialKey string

const (
	CPOCredentialKey  CredentialKey = "cpo"
	EMSPCredentialKey CredentialKey = "emsp"
)

type Connection struct {
	CountryCode string        `json:"country_code"`
	PartyID     string        `json:"party_code"`
	Endpoint    MapEndpoint   `json:"endpoint"`
	Credential  MapCredential `json:"credential"`
}

type CPOInformation struct {
	Token       string
	CommandsURL string
	Version     string
	PartyID     string
	CountryCode string
}

type EMSPInformation struct {
	Token       string
	URL         string
	Version     string
	PartyID     string
	CountryCode string
}

func (c *Connection) GetURL(module string) (string, error) {
	for _, endpoint := range c.Endpoint {
		for _, ei := range endpoint.Endpoints {
			if strings.EqualFold(ei.Identifier, module) {
				return ei.URL, nil
			}
		}
	}
	return "", errors.New("url not found")
}
func (c *Connection) LocationURL(evse EvseData) (string, error) {
	location, errURL := c.GetURL(Locations)
	if errURL != nil {
		return "", nil
	}
	locationID, errLocID := evse.GetLocationID()
	if errLocID != nil {
		return "", errLocID
	}
	url := location + "/" + locationID
	return url, nil
}
func (c *Connection) CPOInformation() *CPOInformation {
	cpoVersion := ""
	cpoCommandsURL := ""
	cpoCountryCode := ""
	cpoPartyID := ""
	for version, endpoint := range c.Endpoint {
		cpoVersion = version
		for _, ei := range endpoint.Endpoints {
			if strings.EqualFold(ei.Identifier, "commands") {
				cpoCommandsURL = ei.URL
			}
		}
	}
	cpoCredential := c.Credential[CPOCredentialKey]
	cpoToken := cpoCredential.Token
	for _, r := range cpoCredential.Roles {
		if strings.EqualFold(r.Role, "cpo") {
			cpoCountryCode = r.CountryCode
			cpoPartyID = r.PartyID
		}
	}
	return &CPOInformation{
		Token:       cpoToken,
		CommandsURL: cpoCommandsURL,
		Version:     cpoVersion,
		PartyID:     cpoPartyID,
		CountryCode: cpoCountryCode,
	}
}

func (c *Connection) EMSPInformation() *EMSPInformation {
	url := ""
	cred := c.Credential[EMSPCredentialKey]
	emspVersionsURL := cred.URL
	url = strings.TrimSuffix(emspVersionsURL, "/versions")
	emspVersion := ""
	for version := range c.Endpoint {
		emspVersion = version
	}

	return &EMSPInformation{
		Token:       cred.Token,
		URL:         url,
		PartyID:     c.PartyID,
		CountryCode: c.CountryCode,
		Version:     emspVersion,
	}
}

type MapCredential map[CredentialKey]Credential
type MapEndpoint map[string]Endpoint

type Credential struct {
	Token string `json:"token,omitempty" `
	URL   string `json:"url,omitempty"`
	Roles []Role `json:"roles,omitempty"`
}

type Endpoint struct {
	Version   string         `json:"version,omitempty"`
	Endpoints []EndpointItem `json:"endpoints,omitempty"`
}

// EndpointItem represents individual endpoint details
type EndpointItem struct {
	Identifier string `json:"identifier,omitempty"`
	URL        string `json:"url,omitempty"`
	Role       string `json:"role,omitempty"`
}

type Role struct {
	Role        string `json:"role"`
	PartyID     string `json:"party_id"`
	CountryCode string `json:"country_code"`
}
