package domain

import (
	"errors"
	"strings"
)

type Status string

const (
	StatusSuccess         Status = "SUCCESS"
	StatusCallbackTimeout Status = "CALLBACK_TIMEOUT"
)

const StatusCodeInvalid = 2000

const (
	UccCommandName         string = "eds-ev.ocpiCommandResponse"
	UccObjectUpdateCommand string = "eds-ev.objectUpdate"
	SyncOcpiObjectEvent           = "syncOcpiObject"
	OcpiCommandEvent              = "ocpiCommand"
)

type UccCommand struct {
	CommandName string    `json:"commandName"`
	Params      UccParams `json:"params"`
	Targets     []string  `json:"targets"`
}

type UccSyncRequest struct {
	CommandName string        `json:"commandName"`
	Params      SyncUccParams `json:"params"`
	Targets     []string      `json:"targets"`
}
type UccParams struct {
	StatusCode      int    `json:"statusCode"`
	ElvisCode       Status `json:"elvisCode"`
	TrackingID      string `json:"trackingId"`
	CommandResponse any    `json:"commandResponse"`
}

type UccCommandResponse struct {
	Data     CreateOperation `json:"data"`
	Metadata Metadata        `json:"metadata"`
}

type Metadata struct {
	// RequestID The unique request ID associated with the response.
	RequestID string `json:"requestId"`
	Total     *int64 `json:"total,omitempty"`
}

type CreateOperation struct {
	OperationID   string            `json:"operationId,omitempty"`
	OperationName string            `json:"operationName,omitempty"`
	TargetErrors  map[string]string `json:"targetErrors,omitempty"`
	TargetFail    int               `json:"targetFail,omitempty"`
	TargetSuccess int               `json:"targetSuccess,omitempty"`
}

type EvseData struct {
	ChargerID   string         `json:"charger_id"`
	Connection  EvseConnection `json:"connection"`
	Environment string         `json:"environment"`
	EvseUID     string         `json:"evse_uid"`
	Site        string         `json:"site"`
	UccID       string         `json:"ucc_id"`
}

func (e *EvseData) GetLocationID() (string, error) {
	parts := strings.Split(e.ChargerID, "/")
	if len(parts) < 2 {
		return "", errors.New("charger id not valid in db (US/GVR/[LocationID])")
	}
	return parts[len(parts)-1], nil
}

type EvseConnection struct {
	CountryCode string `json:"country_code,omitempty"`
	PartyID     string `json:"party_id,omitempty"`
}

type EvseResponse struct {
	Data   []EvseData `json:"data"`
	Status bool       `json:"status"`
}

type SyncUccParams struct {
	Method     string `json:"method"`
	ObjectPath string `json:"object_path"`
	Module     string `json:"module"`
	Value      string `json:"value"`
}

type TrackingInformation struct {
	CountryCode string `json:"country_code,omitempty"`
	PartyID     string `json:"party_id,omitempty"`
	CommandName string `json:"command_name,omitempty"`
	LocationID  string `json:"location_id,omitempty"`
	EvseUID     string `json:"evse_uid,omitempty"`
}
