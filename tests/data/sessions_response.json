{"timestamp": "2025-06-17T09:34:44Z", "data": [{"id": "5", "publish": true, "name": "Site", "address": "7300 W Friendly Ave", "city": "Greensboro", "state": "NC", "country": "USA", "coordinates": {"latitude": "40.7566640", "longitude": "-73.9902850"}, "evses": [{"uid": "24", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "24", "standard": "IEC_62196_T2_COMBO", "format": "CABLE", "power_type": "DC", "max_voltage": 500, "max_amperage": 100, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E24*1", "physical_reference": "testing", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "56", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "56", "standard": "CHADEMO", "format": "CABLE", "power_type": "DC", "max_voltage": 500, "max_amperage": 100, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E24*2", "physical_reference": "testing", "last_updated": "2025-06-16T10:58:01Z"}], "operator": {"name": "Gilbarco US Test"}, "suboperator": {"name": "Gilbarco US Test"}, "owner": {"name": "<PERSON><PERSON><PERSON>"}, "images": [], "country_code": "US", "party_id": "GVR", "postal_code": "27407", "time_zone": "America/New_York", "opening_times": {"twentyfourseven": true}, "energy_mix": {"is_green_energy": false}, "last_updated": "2025-06-17T09:34:44Z"}, {"id": "8", "publish": true, "name": "DOMS Lab DK1", "address": "Formervangen 18", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "AL", "country": "USA", "coordinates": {"latitude": "55.6816994", "longitude": "12.3732123"}, "evses": [{"uid": "40", "status": "CHARGING", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "40", "standard": "IEC_62196_T2_COMBO", "format": "CABLE", "power_type": "DC", "max_voltage": 500, "max_amperage": 100, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E12*2", "floor_level": "1", "physical_reference": "Doms Charger DK", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "12", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "12", "standard": "CHADEMO", "format": "CABLE", "power_type": "DC", "max_voltage": 500, "max_amperage": 100, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E12*1", "floor_level": "1", "physical_reference": "Doms Charger DK", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "63", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "63", "standard": "IEC_62196_T2_COMBO", "format": "CABLE", "power_type": "DC", "max_voltage": 1000, "max_amperage": 200, "max_electric_power": 200000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T16:44:06Z"}], "evse_id": "US*111*E29*1", "physical_reference": "Q011320250020", "last_updated": "2025-06-16T16:44:06Z"}, {"uid": "64", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "64", "standard": "IEC_62196_T2_COMBO", "format": "CABLE", "power_type": "DC", "max_voltage": 1000, "max_amperage": 200, "max_electric_power": 200000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T16:44:06Z"}], "evse_id": "US*111*E29*2", "physical_reference": "Q011320250020", "last_updated": "2025-06-16T16:44:06Z"}, {"uid": "81", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "81", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-17T07:27:04Z"}], "evse_id": "US*111*E38*1", "physical_reference": "PAN_EV1", "last_updated": "2025-06-17T07:27:04Z"}, {"uid": "82", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "82", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:59:37Z"}], "evse_id": "US*111*E38*2", "physical_reference": "PAN_EV1", "last_updated": "2025-06-16T10:59:37Z"}], "operator": {"name": "Gilbarco US Test"}, "suboperator": {"name": "Gilbarco US Test"}, "owner": {"name": "Gilbarco NA"}, "images": [], "country_code": "US", "party_id": "GVR", "postal_code": "26000", "time_zone": "America/New_York", "opening_times": {"twentyfourseven": true}, "energy_mix": {"is_green_energy": false}, "last_updated": "2025-06-17T07:27:04Z"}, {"id": "10", "publish": true, "name": "SIT GSO LAB", "address": "7300 W Friendly Ave", "city": "Greensboro, NC", "state": "NY", "country": "USA", "coordinates": {"latitude": "40.7566640", "longitude": "-73.9902850"}, "evses": [{"uid": "18", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "18", "standard": "IEC_62196_T2", "format": "CABLE", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E18*1", "physical_reference": "Konect_GVR_Vir", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "45", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "45", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E18*2", "physical_reference": "Konect_GVR_Vir", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "46", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "46", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E18*3", "physical_reference": "Konect_GVR_Vir", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "19", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "47", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}, {"id": "48", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}, {"id": "19", "standard": "IEC_62196_T2", "format": "CABLE", "power_type": "AC_1_PHASE", "max_voltage": 230, "max_amperage": 96, "max_electric_power": 22000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E19*1", "physical_reference": "123243", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "25", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "25", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E25*1", "physical_reference": "Konect-SIT-2-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "57", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "57", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E25*2", "physical_reference": "Konect-SIT-2-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "26", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "26", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E26*1", "physical_reference": "<PERSON>_Charger_Te", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "58", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "58", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E26*2", "physical_reference": "<PERSON>_Charger_Te", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "59", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "59", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E27*1", "physical_reference": "Passport-GSO-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "60", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "60", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E27*2", "physical_reference": "Passport-GSO-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "61", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "61", "standard": "TESLA_S", "format": "CABLE", "power_type": "DC", "max_voltage": 1000, "max_amperage": 200, "max_electric_power": 200000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E28*1", "physical_reference": "GSO-SIT-Q1106202", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "62", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "CHARGING_PROFILE_CAPABLE", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "62", "standard": "IEC_62196_T2_COMBO", "format": "CABLE", "power_type": "DC", "max_voltage": 1000, "max_amperage": 200, "max_electric_power": 200000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E28*2", "physical_reference": "GSO-SIT-Q1106202", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "67", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "67", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E31*1", "physical_reference": "PassportNA-00001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "68", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "68", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E31*2", "physical_reference": "PassportNA-00001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "69", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "69", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E32*1", "physical_reference": "GSO-SIT-Test-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "70", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "70", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E32*2", "physical_reference": "GSO-SIT-Test-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "71", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "71", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E33*1", "physical_reference": "PassportNABA-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "72", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "72", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E33*2", "physical_reference": "PassportNABA-000", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "73", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "73", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E34*1", "physical_reference": "GSO-POS-NA-0001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "74", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "74", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E34*2", "physical_reference": "GSO-POS-NA-0001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "75", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "75", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E35*1", "physical_reference": "IND-TRA-NA-0001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "76", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "76", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E35*2", "physical_reference": "IND-TRA-NA-0001", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "77", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "77", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E36*1", "physical_reference": "passport-<PERSON><PERSON>", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "78", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "78", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E36*2", "physical_reference": "passport-<PERSON><PERSON>", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "79", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "79", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 50, "max_electric_power": 11000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E37*1", "physical_reference": "PassportMISI", "last_updated": "2025-06-16T10:58:01Z"}, {"uid": "80", "status": "UNKNOWN", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "80", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 104, "max_electric_power": 50000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T10:58:01Z"}], "evse_id": "US*111*E37*2", "physical_reference": "PassportMISI", "last_updated": "2025-06-16T10:58:01Z"}], "operator": {"name": "Gilbarco US Test"}, "suboperator": {"name": "Gilbarco US Test"}, "owner": {"name": "Gilbarco NA"}, "images": [], "country_code": "US", "party_id": "GVR", "postal_code": "27407", "time_zone": "US/Central", "opening_times": {"twentyfourseven": true}, "energy_mix": {"is_green_energy": false}, "last_updated": "2025-06-16T10:58:01Z"}, {"id": "11", "publish": true, "name": "SIT GSO LAB 2", "address": "7300 W Friendly Ave", "city": "Greensboro, NC", "state": "NY", "country": "USA", "coordinates": {"latitude": "40.7566640", "longitude": "-73.9902850"}, "evses": [{"uid": "15", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "15", "standard": "IEC_62196_T2", "format": "SOCKET", "power_type": "AC_1_PHASE", "max_voltage": 220, "max_amperage": 1818, "max_electric_power": 400000, "tariff_ids": ["cb06464c789ac399dd3bddad07eb2573"], "last_updated": "2025-06-17T01:51:08Z"}], "evse_id": "US*111*E15*1", "physical_reference": "GSO-SIT-NA-00001", "last_updated": "2025-06-17T01:51:08Z"}, {"uid": "43", "status": "AVAILABLE", "capabilities": ["REMOTE_START_STOP_CAPABLE", "RFID_READER", "TOKEN_GROUP_CAPABLE"], "connectors": [{"id": "43", "standard": "IEC_62196_T2_COMBO", "format": "SOCKET", "power_type": "DC", "max_voltage": 480, "max_amperage": 833, "max_electric_power": 400000, "tariff_ids": ["8f76b17872ff51d5bfba7506fa58cf2d"], "last_updated": "2025-06-16T18:14:10Z"}], "evse_id": "US*111*E15*2", "physical_reference": "GSO-SIT-NA-00001", "last_updated": "2025-06-16T18:14:10Z"}], "operator": {"name": "Gilbarco US Test"}, "suboperator": {"name": "Gilbarco US Test"}, "owner": {"name": "Gilbarco NA"}, "images": [], "country_code": "US", "party_id": "GVR", "postal_code": "27407", "time_zone": "US/Central", "opening_times": {"twentyfourseven": true}, "energy_mix": {"is_green_energy": false}, "last_updated": "2025-06-17T01:51:08Z"}], "status_code": 1000, "status_message": "Success"}