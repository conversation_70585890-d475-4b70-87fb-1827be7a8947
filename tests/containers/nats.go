package containers

import (
	"context"
	"fmt"

	"github.com/nats-io/nats.go"
	"github.com/nats-io/nats.go/jetstream"
	natstc "github.com/testcontainers/testcontainers-go/modules/nats"
)

type NATSContainer struct {
	container *natstc.NATSContainer
	js        jetstream.JetStream
	ctx       context.Context
	natsURI   string
}

func NewNatsContainer() *NATSContainer {
	return &NATSContainer{}
}

func (p *NATSContainer) InitContainer() error {
	ctx := context.Background()
	container, err := natstc.Run(ctx, "nats:latest")
	if err != nil {
		return err
	}
	p.container = container

	url := container.MustConnectionString(ctx)
	fmt.Printf("NATS server URL: %s\n", url)

	opts := nats.GetDefaultOptions()
	opts.Url = url
	opts.Pedantic = true
	opts.RetryOnFailedConnect = true
	p.natsURI = url
	nc, err := opts.Connect()
	if err != nil {
		return err
	}

	js, err := jetstream.New(nc)
	if err != nil {
		return err
	}
	p.js = js
	p.ctx = ctx
	return nil
}

func (p *NATSContainer) DestroyContainer() error {
	return p.container.Terminate(p.ctx)
}

func (p *NATSContainer) ServerURL() string {
	return p.natsURI
}

func (p *NATSContainer) CreateStream(name, subject string) (jetstream.Stream, error) {
	return p.js.CreateStream(p.ctx, jetstream.StreamConfig{
		Name:     name,
		Subjects: []string{subject}})
}

func (p *NATSContainer) StreamInfo(name string) (*jetstream.StreamInfo, error) {
	stream, err := p.js.Stream(p.ctx, name)
	if err != nil {
		return nil, err
	}
	return stream.Info(p.ctx)
}

func (p *NATSContainer) PurgeStream(name string) error {
	stream, err := p.js.Stream(p.ctx, name)
	if err != nil {
		return err
	}
	return stream.Purge(p.ctx)
}

func (p *NATSContainer) DeleteStream(name string) error {
	return p.js.DeleteStream(p.ctx, name)
}
