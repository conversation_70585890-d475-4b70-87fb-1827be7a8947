package containers

import (
	"context"

	"github.com/rs/zerolog/log"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

type MongoContainer struct {
	client           *mongo.Client
	mongoURI         string
	mongodbContainer *mongodb.MongoDBContainer
}

func NewMongoContainer() *MongoContainer {
	return &MongoContainer{}
}
func (m *MongoContainer) RunContainer() error {
	ctx := context.Background()
	mongodbContainer, err := mongodb.Run(ctx, "mongo:latest")
	if err != nil {
		log.Printf("failed to start container: %s", err)
		return err
	}
	endpoint, err := mongodbContainer.ConnectionString(ctx)
	if err != nil {
		log.Printf("failed to get connection string: %s", err)
		return err
	}

	clientOpts := options.Client().ApplyURI(endpoint)
	client, err := mongo.Connect(clientOpts)
	if err != nil {
		log.Printf("failed to connect: %v", err)
	}

	// Ping the MongoDB server to verify connection
	if errPing := client.Ping(ctx, nil); errPing != nil {
		log.Printf("failed to ping mongo server: %s", errPing)
		return errPing
	}
	m.client = client
	m.mongoURI = endpoint
	m.mongodbContainer = mongodbContainer
	return nil
}

func (m *MongoContainer) GetClient() *mongo.Client {
	return m.client
}
func (m *MongoContainer) GetConnectionString() string {
	return m.mongoURI
}
func (m *MongoContainer) DeleteContainer() {
	if m.mongodbContainer == nil {
		return
	}
	if err := testcontainers.TerminateContainer(m.mongodbContainer); err != nil {
		log.Printf("failed to terminate container: %s", err)
	}
}
